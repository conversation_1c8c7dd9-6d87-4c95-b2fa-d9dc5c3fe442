RewriteEngine On

<Files "list_kyc_alpha.txt">
    Order Allow,<PERSON>y
    Den<PERSON> from all
</Files>

<Files .env>
    Order allow,deny
    Deny from all
</Files>

<FilesMatch "\.DS_Store$">
    Order allow,deny
    <PERSON><PERSON> from all
</FilesMatch>

#Xử lý Route CTV
RewriteRule ^ctv/([A-Za-z0-9-]+) index.php?module=ctv&action=$1 [L]
RewriteRule ^ctv index.php?module=ctv&action= [L]
#Xử lý Route ADMIN
RewriteRule ^admin/([A-Za-z0-9-]+) index.php?module=admin&action=$1 [L]
RewriteRule ^admin index.php?module=admin&action= [L]
#Xử lý Route CLIENT
RewriteCond %{QUERY_STRING} ^token=([^&]+)&id_product=([^&]+)&key=([^&]+)&order_id=([^&]+)&quantity=([^&]+)
RewriteRule ^api/apiv2/buy_taphoa\.php$ ajaxs/client/product.php?action=buyProduct&api_key=%1&id=%2&amount=%5&is_taphoammo=1 [L,QSA]
RewriteCond %{QUERY_STRING} ^token=([^&]+)&id_product=([^&]+)&key=([^&]+)&order_id=([^&]+)$
RewriteRule ^api/apiv2/buy_taphoa\.php$ ajaxs/client/product.php?action=buyProduct&api_key=%1&id=%2&amount=1&is_taphoammo=1 [L,QSA]
RewriteCond %{QUERY_STRING} ^token=([^&]+)&id_product=([^&]+)
RewriteRule ^api/apiv2/store_taphoa\.php$ api/taphoammo/get_stock.php?api_key=%1&product=%2 [L,QSA]
RewriteRule ^recharge-manual/([A-Za-z0-9-]+) index.php?module=client&action=recharge-manual&slug=$1 [L]
RewriteRule ^login index.php?module=client&action= [L]
RewriteRule ^Dashbroad index.php?module=client&action= [L]
RewriteRule ^Auth/Login index.php?module=client&action= [L]
RewriteRule ^tool/random-face index.php?module=client&action=tool-random-face [L]
RewriteRule ^tool/icon-facebook index.php?module=client&action=tool-icon-facebook [L]
RewriteRule ^tool/get-2fa index.php?module=client&action=tool-2fa [L]
RewriteRule ^tool/check-live-facebook index.php?module=client&action=tool-checklive-fb [L]
RewriteRule ^category/([A-Za-z0-9-]+) index.php?module=client&action=home&slug=$1 [L]
RewriteRule ^api/buy_product ajaxs/client/product.php [L]
RewriteRule ^blogs index.php?module=client&action=blogs [L]
RewriteRule ^blog/([A-Za-z0-9-]+) index.php?module=client&action=blog&slug=$1 [L]
RewriteRule ^document-api index.php?module=client&action=document-api [L]
RewriteRule ^product/([A-Za-z0-9-]+)&aff=([A-Za-z0-9-]+)$ index.php?module=client&action=product&slug=$1&aff=$2 [L]
RewriteRule ^product/([A-Za-z0-9-]+) index.php?module=client&action=product&slug=$1 [L]
RewriteRule ^product-order/([A-Za-z0-9-]+) index.php?module=client&action=product-order&trans_id=$1 [L]
RewriteRule ^product-orders index.php?module=client&action=product-orders [L]
RewriteRule ^join/([A-Za-z0-9-]+) index.php?module=client&action=home&aff=$1 [L]
RewriteRule ^client/home/<USER>
RewriteRule ^payment/([A-Za-z0-9-]+) index.php?module=client&action=payment&trans_id=$1 [L]
RewriteRule ^client/([A-Za-z0-9-]+) index.php?module=client&action=$1 [L]
RewriteRule ^client index.php?module=client&action= [L]
#Xử lý Route COMMON
RewriteRule ^common/([A-Za-z0-9-]+) index.php?module=common&action=$1 [L]