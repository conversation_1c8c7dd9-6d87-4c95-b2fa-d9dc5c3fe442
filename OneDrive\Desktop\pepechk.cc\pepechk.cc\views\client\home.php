<?php if (!defined('IN_SITE')) {
    die('The Request Not Found');
}
$body = [
    'title' => $CMSNT->site('title'),
    'desc'   => $CMSNT->site('description'),
    'keyword' => $CMSNT->site('keywords')
];
$body['header'] = '
<link rel="stylesheet" href="'.BASE_URL('public/client/').'css/wallet.css">
';
$body['footer'] = '
 
';

if($CMSNT->site('isLoginRequiredToViewProduct') == 1) {
    require_once(__DIR__ . '/../../models/is_user.php');
}else{
    if (isSecureCookie('user_login') == true) {
        require_once(__DIR__ . '/../../models/is_user.php');
    }
}

require_once(__DIR__ . '/header-custom.php');
require_once(__DIR__ . '/sidebar-custom.php');
require_once(__DIR__ . '/nav-custom.php');

if (isset($_GET['limit'])) {
    $limit = intval(check_string($_GET['limit']));
} else {
    $limit = 10;
}
if (isset($_GET['page'])) {
    $page = check_string(intval($_GET['page']));
} else {
    $page = 1;
}
$from = ($page - 1) * $limit;
$where = " `status` = 1 ";
$keyword = '';
$shortby = 1;
$category = '';

if (!empty($_GET['category'])) {
    $category = check_string($_GET['category']);
    $where .= ' AND `category_id` = "' . $category . '" ';
}
if (!empty($_GET['keyword'])) {
    $keyword = check_string($_GET['keyword']);
    $where .= ' AND `title` LIKE "%' . $keyword . '%" ';
}
if (!empty($_GET['time'])) {
    $time = check_string($_GET['time']);
    $create_date_1 = str_replace('-', '/', $time);
    $create_date_1 = explode(' to ', $create_date_1);
    if ($create_date_1[0] != $create_date_1[1]) {
        $create_date_1 = [$create_date_1[0] . ' 00:00:00', $create_date_1[1] . ' 23:59:59'];
        $where .= " AND `create_gettime` >= '" . $create_date_1[0] . "' AND `create_gettime` <= '" . $create_date_1[1] . "' ";
    }
}

if (isset($_GET['shortby'])) {
    $shortby = check_string($_GET['shortby']);
}
if ($shortby == 1) {
    $where .= " ORDER BY `id` DESC ";
}
if ($shortby == 2) {
    $where .= " ORDER BY `view` DESC ";
}

$listDatatable = $CMSNT->get_list(" SELECT * FROM `post_notifications` WHERE $where LIMIT $from,$limit ");
$totalDatatable = $CMSNT->num_rows(" SELECT * FROM `post_notifications` WHERE $where ");
?>




<!-- Main Container -->
<main id="kt_app_main" class="app-main flex-column flex-row-fluid">
    <!--begin::Content wrapper-->
    <div class="d-flex flex-column flex-column-fluid">
        <!--begin::Content-->
        <div id="kt_app_content" class="app-content flex-column-fluid">
            <!--begin::Content container-->
            <div class="container-xxl">

                    <!-- Official Partners Section -->
<div class="card bg-dark mb-5">
    <div class="card-body">
        <h4 class="mb-4 d-flex align-items-center">
            <i class="ki-duotone ki-award fs-2 text-primary me-2"></i>
            <span class="text-white">Official Partners</span>
        </h4>
        <div class="row g-3">
            <div class="col-12">
                <div class="rounded-3 overflow-hidden bg-secondary bg-opacity-25" style="height: 100px;">
                    <img src="https://placehold.co/800x100?text=Partner+Banner" class="img-fluid w-100 h-100 object-fit-cover" alt="Official Partner" />
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Partners & Sponsors Section -->
<div class="card bg-dark mb-5">
    <div class="card-body">
        <h4 class="mb-4 d-flex align-items-center">
            <i class="ki-duotone ki-handshake fs-2 text-info me-2"></i>
            <span class="text-white">Partners & Sponsors</span>
        </h4>
        <div class="row g-3">
            <div class="col-12 col-md-6 col-lg-4">
                <div class="rounded-3 overflow-hidden bg-secondary bg-opacity-25" style="height: 80px;">
                    <img src="https://placehold.co/400x80?text=Partner+1+Banner" class="img-fluid w-100 h-100 object-fit-cover" alt="Partner 1" />
                </div>
            </div>
            <div class="col-12 col-md-6 col-lg-4">
                <div class="rounded-3 overflow-hidden bg-secondary bg-opacity-25" style="height: 80px;">
                    <img src="https://placehold.co/400x80?text=Partner+2+Banner" class="img-fluid w-100 h-100 object-fit-cover" alt="Partner 2" />
                </div>
            </div>
            <div class="col-12 col-md-6 col-lg-4">
                <div class="rounded-3 overflow-hidden bg-secondary bg-opacity-25" style="height: 80px;">
                    <img src="https://placehold.co/400x80?text=Partner+3+Banner" class="img-fluid w-100 h-100 object-fit-cover" alt="Partner 3" />
                </div>
            </div>
            <!-- Add more banners as needed -->
        </div>
    </div>
</div>
                <!--begin::Card-->
                <div class="card mb-5 mb-xl-10">
                    <!--begin::Card header-->
                    <div class="card-header border-0 pt-5">
                        <h3 class="card-title align-items-start flex-column">
                            <span class="card-label fw-bold fs-3 mb-1">Latest News</span>
                            <span class="text-muted mt-1 fw-semibold fs-7">Stay updated with our latest announcements and updates</span>
                        </h3>
                    </div>
                    <!--end::Card header-->
                    <!--begin::Card body-->
                    <div class="card-body py-4">
                        <!--begin::Table-->
                        <div class="table-responsive">
                            <table class="table align-middle table-row-dashed fs-6 gy-5" id="kt_table_news">
                                <thead>
                                    <tr class="text-start text-muted fw-bold fs-7 text-uppercase gs-0">
                                        <th class="min-w-400px">Content</th>
                                        <th class="min-w-150px text-center">Date</th>
                                    </tr>
                                </thead>
                                <tbody class="text-gray-600 fw-semibold">
                                    <?php foreach ($listDatatable as $row): ?>
                                        <tr>
                                            <td>
                                                <div class="d-flex flex-column">
                                                    <span class="text-gray-800 fw-bold fs-6"><?= base64_decode($row['content']); ?></span>
                                                </div>
                                            </td>
                                            <td class="text-center">
                                                <span class="badge badge-light-primary fw-bold fs-7 px-3 py-2">
                                                    <i class="ki-duotone ki-calendar fs-5 me-1">
                                                        <span class="path1"></span>
                                                        <span class="path2"></span>
                                                    </i>
                                                    <?= $row['create_gettime']; ?>
                                                </span>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                        <!--end::Table-->
                    </div>
                    <!--end::Card body-->
                </div>
                <!--end::Card-->
            </div>
            <!--end::Content container-->
        </div>
        <!--end::Content-->
    </div>
    <!--end::Content wrapper-->
</main>

<?php if($CMSNT->site('popup_status') == 1):?>
<!--begin::Modal - Notification-->
<div class="modal fade" id="modal_notification" tabindex="-1" aria-hidden="true">
    <!--begin::Modal dialog-->
    <div class="modal-dialog modal-dialog-centered">
        <!--begin::Modal content-->
        <div class="modal-content rounded">
            <!--begin::Modal header-->
            <div class="modal-header py-7 d-flex justify-content-between">
            <h2>Notifications</h2>
                <!--begin::Close-->
                <div class="btn btn-sm btn-icon btn-active-color-primary" data-bs-dismiss="modal">
                    <i class="ki-duotone ki-cross fs-1">
                        <span class="path1"></span>
                        <span class="path2"></span>
                    </i>
                </div>
                <!--end::Close-->
            </div>
            <!--end::Modal header-->
            <!--begin::Modal body-->
            <div class="modal-body scroll-y px-10 px-lg-15 pt-0 pb-15">
                <!--begin::Heading-->
                <div class="text-center mb-13">
                    <div class="text-muted fw-semibold fs-5">
                        <br>
                        <?=$CMSNT->site('popup_noti');?>
                    </div>
                </div>
                <!--end::Heading-->
                <!--begin::Actions-->
                <div class="d-flex flex-center flex-row-fluid pt-12">
                    <button type="button" class="btn btn-light-primary me-3" data-bs-dismiss="modal">Close</button>
                    <button type="button" class="btn btn-primary" id="dontShowAgainBtn">
                        <i class="ki-duotone ki-eye-slash fs-2 me-1">
                            <span class="path1"></span>
                            <span class="path2"></span>
                        </i>
                        <?=__('Hide for 2 hours');?>
                    </button>
                </div>
                <!--end::Actions-->
            </div>
            <!--end::Modal body-->
        </div>
        <!--end::Modal content-->
    </div>
    <!--end::Modal dialog-->
</div>
<!--end::Modal - Notification-->
<script>
document.addEventListener("DOMContentLoaded", function() {
    var modal = document.getElementById('modal_notification');
    var dontShowAgainBtn = document.getElementById('dontShowAgainBtn');
    var modalClosedTime = localStorage.getItem('modalClosedTime');

    // Show modal if not closed in last 2 hours
    if (!modalClosedTime || (Date.now() - parseInt(modalClosedTime) > 2 * 60 * 60 * 1000)) {
        var bootstrapModal = new bootstrap.Modal(modal);
        bootstrapModal.show();
    }

    // Save close time and hide modal
    dontShowAgainBtn.addEventListener('click', function() {
        localStorage.setItem('modalClosedTime', Date.now());
        var bootstrapModal = bootstrap.Modal.getInstance(modal);
        bootstrapModal.hide();
    });
});
</script>
<?php endif?>


<?php
require_once(__DIR__.'/footer-custom.php');
?>